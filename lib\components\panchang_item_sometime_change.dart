import 'package:flutter/material.dart';
import 'package:panchang_at_this_moment/API/panchang_api.dart';
import 'package:panchang_at_this_moment/data/panchang_stats.dart';
import 'package:panchang_at_this_moment/utils.dart';
import 'package:panchang_at_this_moment/utils/countdown_dialog.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class CurrentPanchangItem {
  final String name;
  final DateTime start;
  final DateTime end;

  CurrentPanchangItem(
      {required this.end, required this.start, required this.name});
}

class CurrentItem{
  final String name;

  CurrentItem({required this.name});
}

// Helper class for muhurta configuration
class MuhurtaConfig {
  final String nameKey;
  final String Function(AppLocalizations) getDisplayName;
  final PanchangSomeTimeItem? Function(PanchangData) getMuhurta;

  MuhurtaConfig({
    required this.nameKey,
    required this.getDisplayName,
    required this.getMuhurta,
  });
}

Widget? getPanchangItemSomeTimeChange(  
    {required PanchangDataForThreeDays panchangData,
    required DateTime currentTime,
    required TextStyle titleTextStyle,
    required TextStyle smallTextStyle,
    required BuildContext context}) {
  final List<Widget> panchangItems = [];
  final List<CurrentItem> panchangSelectedItems = [];
  final List<CurrentPanchangItem> panchangSelectedItemsWithLocalizedNames = [];
  final panchangStats = getPanchangStats(context);

  // Define muhurta configurations
  final muhurtaConfigs = [
    MuhurtaConfig(
      nameKey: 'bramhaMuhrat',
      getDisplayName: (loc) => loc.bramhaMuhrat,
      getMuhurta: (data) => data.bramhaMuhrat,
    ),
    MuhurtaConfig(
      nameKey: 'abhijit',
      getDisplayName: (loc) => loc.abhijit,
      getMuhurta: (data) => data.abhijit,
    ),
    MuhurtaConfig(
      nameKey: 'godhuli',
      getDisplayName: (loc) => loc.godhuli,
      getMuhurta: (data) => data.godhuli,
    ),
    MuhurtaConfig(
      nameKey: 'pratahSandhya',
      getDisplayName: (loc) => loc.pratahSandhya,
      getMuhurta: (data) => data.pratahSandhya,
    ),
    MuhurtaConfig(
      nameKey: 'vijayMuhurat',
      getDisplayName: (loc) => loc.vijayMuhurat,
      getMuhurta: (data) => data.vijayMuhurat,
    ),
    MuhurtaConfig(
      nameKey: 'sayahnaSandhya',
      getDisplayName: (loc) => loc.sayahnaSandhya,
      getMuhurta: (data) => data.sayahnaSandhya,
    ),
    MuhurtaConfig(
      nameKey: 'nishitaMuhurta',
      getDisplayName: (loc) => loc.nishitaMuhurta,
      getMuhurta: (data) => data.nishitaMuhurta,
    ),
    MuhurtaConfig(
      nameKey: 'rahuKal',
      getDisplayName: (loc) => loc.rahuKal,
      getMuhurta: (data) => data.rahuKal,
    ),
    MuhurtaConfig(
      nameKey: 'gulikaiKal',
      getDisplayName: (loc) => loc.gulikaiKal,
      getMuhurta: (data) => data.gulikaiKal,
    ),
    MuhurtaConfig(
      nameKey: 'yamaganda',
      getDisplayName: (loc) => loc.yamaganda,
      getMuhurta: (data) => data.yamaganda,
    ),
  ];

  // Helper function to add muhurta items
  void addMuhurtaItem(String nameKey, String displayName, DateTime start, DateTime end) {
    panchangItems.add(Text(
      displayName,
      style: titleTextStyle.copyWith(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: (panchangStats[nameKey] as Map)['isPositive'] ? Colors.green : Colors.red,
      ),
    ));
    panchangSelectedItemsWithLocalizedNames.add(CurrentPanchangItem(
      name: displayName,
      start: start,
      end: end,
    ));
    panchangSelectedItems.add(CurrentItem(name: nameKey));
  }

  // Process all muhurta configurations
  final localizations = AppLocalizations.of(context)!;
  for (final dayData in [panchangData.previousDay, panchangData.currentDay, panchangData.nextDay]) {
    for (final config in muhurtaConfigs) {
      final muhurta = config.getMuhurta(dayData);
      if (muhurta != null && _isCurrent(muhurta, currentTime)) {
        addMuhurtaItem(
          config.nameKey,
          config.getDisplayName(localizations),
          muhurta.start,
          muhurta.end,
        );
      }
    }

    // Handle special case for durMuhurtam (list of items)
    if (dayData.durMuhurtam.any((element) => _isCurrent(element, currentTime))) {
      final current = dayData.durMuhurtam.firstWhere((element) => _isCurrent(element, currentTime));
      addMuhurtaItem(
        'durMuhurtam',
        localizations.durMuhurtam,
        current.start,
        current.end,
      );
    }

    // Handle varjyam (list of items)
    if (dayData.varjyam.any((element) => _isCurrent(element, currentTime))) {
      final current = dayData.varjyam.firstWhere((element) => _isCurrent(element, currentTime));
      addMuhurtaItem(
        'varjyam',
        localizations.varjyam,
        current.start,
        current.end,
      );
    }

    // Handle amritKal (list of items)
    if (dayData.amritKal.any((element) => _isCurrent(element, currentTime))) {
      final current = dayData.amritKal.firstWhere((element) => _isCurrent(element, currentTime));
      addMuhurtaItem(
        'amritKal',
        localizations.amritKal,
        current.start,
        current.end,
      );
    }
  }

  // Calculate statistics for display
  final List<String> panchangTips = [];
  double good = 0;
  double bad = 0;

  for (final item in panchangSelectedItems) {
    final stats = panchangStats[item.name] as Map;
    if (stats['isPositive']) {
      good += stats['score'];
    } else {
      bad += stats['score'];
    }
    panchangTips.add(stats['tip']);
  }

  final double total = good + (bad * -1);
  final double goodRatio = total > 0 ? (good / total) * 100 : 0;
  final double badRatio = total > 0 ? (bad * -1 / total) * 100 : 0;

  return (panchangItems.isNotEmpty)
      ? CountDownWithTimeDialog(
          showMinutesFormattedTime: true,
          currentTime: currentTime,
          countDownItems: panchangSelectedItemsWithLocalizedNames
              .map((item) => CountDownItem(
                    title: item.name,
                    endTime: item.end,
                    startTime: item.start,
                  ))
              .toList(),
          child: Container(
            color: Colors.transparent,
            child: Column(
              children: [
                Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Column(
                        children: panchangItems,
                      ),
                      const SizedBox(
                        width: 10,
                      ),
                      Column(
                        children: [
                          Text(
                            "+ve score : ${bad + good}",
                            style: smallTextStyle,
                          ),
                          const SizedBox(
                            height: 2,
                          ),
                          GoodAndBadCircle(
                            good: goodRatio,
                            bad: badRatio,
                          ),
                        ],
                      ),
                    ]),
                const SizedBox(
                  height: 10,
                ),
                CirculatingText(
                  texts: panchangTips,
                )
              ],
            ),
          ))
      : null;
}

bool _isCurrent(PanchangSomeTimeItem interval, DateTime currentTime) {
  return interval.start.isBefore(currentTime) &&
      interval.end.isAfter(currentTime);
}
